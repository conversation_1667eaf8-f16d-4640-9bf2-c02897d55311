"""
GitHub Connector

This module provides comprehensive GitHub integration including:
- Repository, issue, and pull request synchronization
- Code content extraction and processing
- Permission and collaboration management
- Semantic search capabilities
- Real-time updates and incremental sync

Main Components:
- GitHubConnectorService: Main connector implementation
- GitHubConnection: Connection management
- Schema definitions and constants
- Entity and relationship types
"""

from .service import GitHubConnectorService
from .connection import GitHubConnection
from .schema import GitHubConnectorConfig, GitHubRepository, GitHubIssue

__all__ = [
    'GitHubConnectorService',
    'GitHubConnection',
    'GitHubConnectorConfig',
    'GitHubRepository',
    'GitHubIssue'
]