"""
GitHub Connector Entity Type Definitions with Relevance Grouping

This module defines all entity types for the GitHub connector organized by relevance.
"""

from enum import Enum


class EntityType(Enum):
    """
    GitHub entity types for knowledge graph nodes.

    Define all possible node types that can be created in the knowledge graph
    for GitHub data, organized by relevance groups.
    """

    # Core entities (Tier 1) - Most important for basic functionality
    GITHUB_REPOSITORY = "GitHubRepository"
    GITHUB_USER = "GitHubUser"
    GITHUB_CODE_FILE = "GitHubCodeFile"
    GITHUB_COMMIT = "GitHubCommit"

    # Essential entities (Tier 2) - Important for comprehensive functionality
    GITHUB_ORGANIZATION = "GitHubOrganization"
    GITHUB_ISSUE = "GitHubIssue"
    GITHUB_PULL_REQUEST = "GitHubPullRequest"
    GITHUB_BRANCH = "GitHubBranch"

    # Extended entities (Tier 3) - Additional functionality
    GITHUB_DIRECTORY = "GitHubDirectory"
    GITHUB_TEAM = "GitHubTeam"
    GITHUB_TAG = "GitHubTag"
    GITHUB_RELEASE = "GitHubRelease"
    GITHUB_COMMENT = "GitHubComment"
    GITHUB_REVIEW = "GitHubReview"

    # Advanced entities (Tier 4) - Specialized functionality
    GITHUB_REVIEW_COMMENT = "GitHubReviewComment"



def get_all_entity_types():
    """
    Returns all GitHub entity types for connector registration.

    Returns:
        set: Set of all entity type string values
    """
    return {e.value for e in EntityType}



def get_core_repository_entities():
    """
    Returns core repository-related entity types.
    Primary organizational structures.

    Returns:
        set: Set of repository entity type string values
    """
    return {
        EntityType.GITHUB_REPOSITORY.value,
        EntityType.GITHUB_ORGANIZATION.value,
        EntityType.GITHUB_USER.value,
        EntityType.GITHUB_TEAM.value,
    }


def get_code_structure_entities():
    """
    Returns code structure-related entity types.
    File system and code organization.

    Returns:
        set: Set of code structure entity type string values
    """
    return {
        EntityType.GITHUB_CODE_FILE.value,
        EntityType.GITHUB_DIRECTORY.value,
    }


def get_version_control_entities():
    """
    Returns version control-related entity types.
    Git workflow and history tracking.

    Returns:
        set: Set of version control entity type string values
    """
    return {
        EntityType.GITHUB_COMMIT.value,
        EntityType.GITHUB_BRANCH.value,
        EntityType.GITHUB_TAG.value,
        EntityType.GITHUB_RELEASE.value,
    }


def get_collaboration_entities():
    """
    Returns collaboration-related entity types.
    Issue tracking and pull requests.

    Returns:
        set: Set of collaboration entity type string values
    """
    return {
        EntityType.GITHUB_ISSUE.value,
        EntityType.GITHUB_PULL_REQUEST.value,
        EntityType.GITHUB_COMMENT.value,
        EntityType.GITHUB_REVIEW.value,
        EntityType.GITHUB_REVIEW_COMMENT.value,
    }



def is_code_entity(entity_type: str) -> bool:
    """
    Check if an entity type represents code.

    Args:
        entity_type: The entity type string

    Returns:
        bool: True if it's a code entity, False otherwise
    """
    return entity_type in {
        EntityType.GITHUB_CODE_FILE.value,
        EntityType.GITHUB_DIRECTORY.value,
    }


def is_repository_entity(entity_type: str) -> bool:
    """
    Check if an entity type represents a repository.

    Args:
        entity_type: The entity type string

    Returns:
        bool: True if it's a repository entity, False otherwise
    """
    return entity_type == EntityType.GITHUB_REPOSITORY.value


def is_issue_or_pr_entity(entity_type: str) -> bool:
    """
    Check if an entity type represents an issue or pull request.

    Args:
        entity_type: The entity type string

    Returns:
        bool: True if it's an issue/PR entity, False otherwise
    """
    return entity_type in {
        EntityType.GITHUB_ISSUE.value,
        EntityType.GITHUB_PULL_REQUEST.value,
    }


def get_entity_type_from_github_type(github_type: str) -> str:
    """
    Get the appropriate entity type based on GitHub API type.

    Args:
        github_type: The type returned by GitHub API

    Returns:
        str: The corresponding entity type
    """
    github_type_mapping = {
        "repository": EntityType.GITHUB_REPOSITORY.value,
        "organization": EntityType.GITHUB_ORGANIZATION.value,
        "user": EntityType.GITHUB_USER.value,
        "team": EntityType.GITHUB_TEAM.value,
        "commit": EntityType.GITHUB_COMMIT.value,
        "branch": EntityType.GITHUB_BRANCH.value,
        "tag": EntityType.GITHUB_TAG.value,
        "release": EntityType.GITHUB_RELEASE.value,
        "issue": EntityType.GITHUB_ISSUE.value,
        "pull_request": EntityType.GITHUB_PULL_REQUEST.value,
        "file": EntityType.GITHUB_CODE_FILE.value,
        "dir": EntityType.GITHUB_DIRECTORY.value,
    }

    return github_type_mapping.get(
        github_type.lower(), EntityType.GITHUB_CODE_FILE.value
    )


def get_entity_type_from_file_extension(file_extension: str) -> str:
    """
    Get the appropriate entity type based on file extension.

    Args:
        file_extension: The file extension (e.g., '.py', '.js')

    Returns:
        str: The corresponding entity type
    """
    # Always return code file for the simplified version
    return EntityType.GITHUB_CODE_FILE.value


def is_processable_file(file_path: str, file_type: str = None) -> bool:
    """
    Check if a file is processable for content extraction.

    Args:
        file_path: The file path
        file_type: Optional file type from GitHub API

    Returns:
        bool: True if the file can be processed for content extraction
    """
    if file_type and file_type.lower() == "dir":
        return False

    # Get file extension
    file_extension = file_path.split(".")[-1].lower() if "." in file_path else ""

    processable_extensions = {
        # Code files
        "py",
        "js",
        "ts",
        "java",
        "cpp",
        "c",
        "h",
        "hpp",
        "cs",
        "php",
        "rb",
        "go",
        "rs",
        "swift",
        "kt",
        "scala",
        "clj",
        "hs",
        "ml",
        "r",
        "matlab",
        "m",
        "pl",
        "sh",
        "bash",
        "zsh",
        "fish",
        "sql",
        "html",
        "css",
        "scss",
        "sass",
        "less",
        "vue",
        "jsx",
        "tsx",
        # Data files
        "json",
        "xml",
        "yaml",
        "yml",
        "toml",
        "ini",
        "cfg",
        "conf",
        "csv",
        "tsv",
        # Documentation
        "md",
        "rst",
        "txt",
        "adoc",
        "asciidoc",
        "org",
        "wiki",
        # Config files
        "dockerfile",
        "makefile",
        "rakefile",
        "gemfile",
        "pipfile",
        "requirements",
        # GitHub specific
        "gitignore",
        "gitattributes",
        "gitmodules",
    }

    # Check common config files without extensions
    config_files = {
        "dockerfile",
        "makefile",
        "rakefile",
        "gemfile",
        "pipfile",
        "requirements.txt",
        ".gitignore",
        ".gitattributes",
        ".gitmodules",
        "license",
        "readme",
        "changelog",
        "contributing",
        "code_of_conduct",
        "security",
        "support",
    }

    file_name = file_path.split("/")[-1].lower()

    return (
        file_extension in processable_extensions
        or file_name in config_files
        or any(file_name.startswith(cf) for cf in config_files)
    )


def get_content_type_from_file(file_path: str) -> str:
    """
    Determine the content type based on file path.

    Args:
        file_path: The file path

    Returns:
        str: The content type (code, documentation, config, data)
    """
    file_extension = file_path.split(".")[-1].lower() if "." in file_path else ""
    file_name = file_path.split("/")[-1].lower()

    code_extensions = {
        "py",
        "js",
        "ts",
        "java",
        "cpp",
        "c",
        "h",
        "hpp",
        "cs",
        "php",
        "rb",
        "go",
        "rs",
        "swift",
        "kt",
        "scala",
        "clj",
        "hs",
        "ml",
        "r",
        "matlab",
        "m",
        "pl",
        "sh",
        "bash",
        "zsh",
        "fish",
        "sql",
        "html",
        "css",
        "scss",
        "sass",
        "less",
        "vue",
        "jsx",
        "tsx",
    }

    documentation_extensions = {"md", "rst", "txt", "adoc", "asciidoc", "org", "wiki"}

    config_extensions = {"json", "xml", "yaml", "yml", "toml", "ini", "cfg", "conf"}

    data_extensions = {"csv", "tsv", "log"}

    if file_extension in code_extensions:
        return "code"
    elif file_extension in documentation_extensions:
        return "documentation"
    elif file_extension in config_extensions:
        return "config"
    elif file_extension in data_extensions:
        return "data"
    elif any(
        doc_word in file_name
        for doc_word in ["readme", "license", "changelog", "contributing"]
    ):
        return "documentation"
    else:
        return "code"  # Default to code



def get_comprehensive_entities():
    """
    Returns all entities for comprehensive GitHub integration.
    Includes all tiers for full functionality.

    Returns:
        set: Set of all entity type string values
    """
    return get_all_entity_types()
