"""
GitHub-specific relationship types for the knowledge graph.
This module defines and categorizes all relationship types for the GitHub connector,
following a structured model for easier management and querying.
"""

from enum import Enum
from typing import Dict, List, Set, Tuple


class GitHubRelationshipType(Enum):
    """GitHub-specific relationship types"""

    # Repository relationships
    OWNS_REPOSITORY = "owns_repository"
    CONTRIBUTES_TO = "contributes_to"
    FORKS_FROM = "forks_from"
    STARS = "stars"
    WATCHES = "watches"
    ORG_OWNS_REPO = "org_owns_repo"  # Explicitly part of this category

    # Issue relationships
    CREATES_ISSUE = "creates_issue"
    CLOSES_ISSUE = "closes_issue"
    FIXES_ISSUE = "fixes_issue"

    # Pull Request relationships
    CREATES_PULL_REQUEST = "creates_pull_request"
    REVIEWS_PULL_REQUEST = "reviews_pull_request"
    MERGES_PULL_REQUEST = "merges_pull_request"
    APPROVES_PR = "approves_pr"
    REQUESTS_CHANGES_PR = "requests_changes_pr"
    COMMIT_IN_PR = "commit_in_pr"

    # Commit relationships
    AUTHORS_COMMIT = "authors_commit"
    COMMITS_TO_BRANCH = "commits_to_branch"
    PARENT_COMMIT = "parent_commit"

    # Branch relationships
    CREATES_BRANCH = "creates_branch"
    MERGES_BRANCH = "merges_branch"
    BRANCH_FROM = "branch_from"
    PROTECTS_BRANCH = "protects_branch"

    # File, Directory, and Code Structure relationships
    MODIFIES_FILE = "modifies_file"
    CREATES_FILE = "creates_file"
    DELETES_FILE = "deletes_file"
    CONTAINS_FILE = "contains_file"
    CONTAINS_DIRECTORY = "contains_directory"
    PARENT_DIRECTORY = "parent_directory"
    BELONGS_TO = "belongs_to"  # Generic inverse for CONTAINS relationships

    # Release relationships
    CREATES_RELEASE = "creates_release"
    TAGS_RELEASE = "tags_release"

    # Workflow relationships
    TRIGGERS_WORKFLOW = "triggers_workflow"
    RUNS_WORKFLOW = "runs_workflow"

    # Organization and Team relationships
    MEMBER_OF_ORG = "member_of_org"
    ADMIN_OF_ORG = "admin_of_org"
    MEMBER_OF_TEAM = "member_of_team"
    ADMIN_OF_TEAM = "admin_of_team"
    TEAM_HAS_ACCESS = "team_has_access"

    # Generic & Social relationships
    COMMENTS_ON = "comments_on"
    REPLY_TO_COMMENT = "reply_to_comment"
    ASSIGNED_TO = "assigned_to"
    MENTIONS = "mentions"
    LINKS_TO = "links_to"
    DEPENDS_ON = "depends_on"
    SIMILAR_TO = "similar_to"


# --- Categorization Functions ---


def get_all_relationship_types() -> Set[str]:
    """Returns all GitHub relationship types for connector registration."""
    return {r.value for r in GitHubRelationshipType}


def get_repository_relationship_types() -> Set[str]:
    """Returns repository-centric relationship types."""
    return {
        GitHubRelationshipType.OWNS_REPOSITORY.value,
        GitHubRelationshipType.CONTRIBUTES_TO.value,
        GitHubRelationshipType.FORKS_FROM.value,
        GitHubRelationshipType.STARS.value,
        GitHubRelationshipType.WATCHES.value,
        GitHubRelationshipType.ORG_OWNS_REPO.value,
        GitHubRelationshipType.PROTECTS_BRANCH.value,
    }


def get_issue_relationship_types() -> Set[str]:
    """Returns issue-centric relationship types."""
    return {
        GitHubRelationshipType.CREATES_ISSUE.value,
        GitHubRelationshipType.CLOSES_ISSUE.value,
        GitHubRelationshipType.FIXES_ISSUE.value,
    }


def get_pr_relationship_types() -> Set[str]:
    """Returns Pull Request-centric relationship types."""
    return {
        GitHubRelationshipType.CREATES_PULL_REQUEST.value,
        GitHubRelationshipType.REVIEWS_PULL_REQUEST.value,
        GitHubRelationshipType.MERGES_PULL_REQUEST.value,
        GitHubRelationshipType.APPROVES_PR.value,
        GitHubRelationshipType.REQUESTS_CHANGES_PR.value,
        GitHubRelationshipType.COMMIT_IN_PR.value,
    }


def get_commit_relationship_types() -> Set[str]:
    """Returns commit-centric relationship types."""
    return {
        GitHubRelationshipType.AUTHORS_COMMIT.value,
        GitHubRelationshipType.COMMITS_TO_BRANCH.value,
        GitHubRelationshipType.PARENT_COMMIT.value,
    }


def get_code_relationship_types() -> Set[str]:
    """Returns file, directory, and code structure relationship types."""
    return {
        GitHubRelationshipType.MODIFIES_FILE.value,
        GitHubRelationshipType.CREATES_FILE.value,
        GitHubRelationshipType.DELETES_FILE.value,
        GitHubRelationshipType.CONTAINS_FILE.value,
        GitHubRelationshipType.CONTAINS_DIRECTORY.value,
        GitHubRelationshipType.PARENT_DIRECTORY.value,
        GitHubRelationshipType.BELONGS_TO.value,
    }


def get_organization_relationship_types() -> Set[str]:
    """Returns organization and team-centric relationship types."""
    return {
        GitHubRelationshipType.MEMBER_OF_ORG.value,
        GitHubRelationshipType.ADMIN_OF_ORG.value,
        GitHubRelationshipType.MEMBER_OF_TEAM.value,
        GitHubRelationshipType.ADMIN_OF_TEAM.value,
        GitHubRelationshipType.TEAM_HAS_ACCESS.value,
    }


def get_generic_relationship_types() -> Set[str]:
    """Returns generic and social interaction relationship types."""
    return {
        GitHubRelationshipType.COMMENTS_ON.value,
        GitHubRelationshipType.REPLY_TO_COMMENT.value,
        GitHubRelationshipType.ASSIGNED_TO.value,
        GitHubRelationshipType.MENTIONS.value,
        GitHubRelationshipType.LINKS_TO.value,
        GitHubRelationshipType.DEPENDS_ON.value,
        GitHubRelationshipType.SIMILAR_TO.value,
    }


# --- Inverse and Bidirectional Logic ---


def is_bidirectional_relationship(relationship_type: str) -> bool:
    """Check if a relationship type is bidirectional (symmetric)."""
    bidirectional_relationships = {
        GitHubRelationshipType.SIMILAR_TO.value,
        GitHubRelationshipType.LINKS_TO.value,
    }
    return relationship_type in bidirectional_relationships


def get_inverse_relationship(relationship_type: str) -> str:
    """Get the inverse of a relationship type if it exists."""
    inverse_map = {
        GitHubRelationshipType.CONTAINS_FILE.value: GitHubRelationshipType.BELONGS_TO.value,
        GitHubRelationshipType.CONTAINS_DIRECTORY.value: GitHubRelationshipType.BELONGS_TO.value,
        GitHubRelationshipType.BELONGS_TO.value: GitHubRelationshipType.CONTAINS_FILE.value,  # Pointing to the most common case
        GitHubRelationshipType.FORKS_FROM.value: "has_fork",  # Conceptual inverse
        GitHubRelationshipType.BRANCH_FROM.value: "has_branch",  # Conceptual inverse
    }
    return inverse_map.get(relationship_type, relationship_type)


def get_relationship_category(relationship_type: str) -> str:
    """Get the category of a relationship type."""
    if relationship_type in get_repository_relationship_types():
        return "repository"
    elif relationship_type in get_issue_relationship_types():
        return "issue"
    elif relationship_type in get_pr_relationship_types():
        return "pull_request"
    elif relationship_type in get_commit_relationship_types():
        return "commit"
    elif relationship_type in get_code_relationship_types():
        return "code"
    elif relationship_type in get_organization_relationship_types():
        return "organization"
    elif relationship_type in get_generic_relationship_types():
        return "generic"
    else:
        return "other"

