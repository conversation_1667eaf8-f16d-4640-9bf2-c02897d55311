"""
GitHub Connector Repository

This module contains database query classes for the GitHub connector,
following Google Drive patterns for consistency.
"""

from .github_queries import (
    GitHubUserQueries,
    GitHubOrganizationQueries,
    GitHubRepositoryQueries,
    GitHubIssueQueries,
    GitHubPullRequestQueries,
    GitHubCommitQueries,
    GitHubFileQueries,
    GitHubTeamQueries,
    GitHubTagQueries,
    GitHubReleaseQueries,
    GitHubCommentQueries,
    GitHubReviewQueries,
    GitHubWorkflowQueries,
    GitHubRelationshipQueries,
    GitHubSyncQueries
)

__all__ = [
    'GitHubUserQueries',
    'GitHubOrganizationQueries',
    'GitHubRepositoryQueries',
    'GitHubIssueQueries',
    'GitHubPullRequestQueries',
    'GitHubCommitQueries',
    'GitHubFileQueries',
    'GitHubTeamQueries',
    'GitHubTagQueries',
    'GitHubReleaseQueries',
    'GitHubCommentQueries',
    'GitHubReviewQueries',
    'GitHubWorkflowQueries',
    'GitHubRelationshipQueries',
    'GitHubSyncQueries'
]
