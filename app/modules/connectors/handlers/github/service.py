"""
GitHub Connector Service

Main service implementation for the GitHub connector following the Google Drive pattern.
Provides comprehensive GitHub integration including repositories, issues, pull requests,
commits, and organizational entity integration.
"""

import logging
from datetime import datetime
from typing import Dict, Any, Optional, Generator, List

from app.modules.connectors.base import BaseConnector
from app.modules.connectors.handlers.github.constants.entities import get_all_entity_types
from app.modules.connectors.handlers.github.constants.relationships import get_all_relationship_types
from app.modules.connectors.handlers.github.connection import GitHubConnection
from app.modules.connectors.handlers.github.schema import GitHubConnectorConfig
from app.modules.connectors.utilities.constant.schemas import (
    ConnectorSearchResponse,
    SearchResultItem,
    SearchStatus,
    SearchMetrics,
    SearchError
)
from app.modules.connectors.handlers.github.services.github_service import GitHubService

# Configure logging
logger = logging.getLogger(__name__)

# Default configuration following Google Drive pattern
DEFAULT_CONFIG = {
    'timeout_seconds': 30,
    'rate_limit_per_hour': 5000,
    'batch_size': 100,
    'full_sync': False,
    'sync_repositories': True,
    'sync_issues': True,
    'sync_pull_requests': True,
    'sync_commits': True,
    'sync_users': True,
    'sync_teams': True,
    'sync_workflows': True,
    'sync_discussions': True,
    'extract_text': True,
    'generate_embeddings': True,
    'chunk_size': 1000
}


class GitHubConnectorService(BaseConnector):
    """
    GitHub Connector Service

    Main connector implementation that follows the Google Drive pattern for consistency.
    Provides GitHub data integration with organizational entities.
    """

    # Connector metadata
    CONNECTOR_TYPE = "code_repository"

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize GitHub connector service.

        Args:
            config: Configuration dictionary, uses defaults if not provided
        """
        self.config = {**DEFAULT_CONFIG, **(config or {})}
        self.github_config = GitHubConnectorConfig(**self.config)

        # Initialize connection
        self.connection = None
        self._connected = False

        # Connector metadata
        self.source_type = "github"
        self.connector_name = "GitHub"
        self.version = "1.0.0"

        # Initialize the existing GitHub service for functionality
        self._github_service = GitHubService()

        logger.info(f"Initialized {self.connector_name} connector v{self.version}")

    def connect(self) -> Any:
        """
        Establish connection to GitHub API and validate credentials.

        Returns:
            GitHubConnection: Connected API client

        Raises:
            ConnectionError: If connection fails
        """
        try:
            logger.info("Connecting to GitHub API...")

            if not self.connection:
                self.connection = GitHubConnection(self.config)

            if self.connection.connect():
                self._connected = True
                logger.info("Successfully connected to GitHub API")
                return self.connection
            else:
                raise ConnectionError("Failed to establish GitHub API connection")

        except Exception as e:
            logger.error(f"GitHub connection failed: {str(e)}")
            self._connected = False
            raise

    def get_connector(self) -> dict:
        """
        Returns metadata about the GitHub connector.

        Returns:
            dict: Connector metadata information
        """
        return {
            "source_type": self.source_type,
            "name": self.connector_name,
            "version": self.version,
            "connector_type": self.CONNECTOR_TYPE,
            "description": "Comprehensive GitHub connector for repository, issue, and pull request synchronization, search, and content processing.",
            "supported_entities": list(get_all_entity_types()),
            "supported_relationships": list(get_all_relationship_types()),
            "capabilities": [
                "data_fetching",
                "search",
                "sync",
                "incremental_sync",
                "repository_processing",
                "entity_extraction",
                "relationship_mapping",
                "semantic_search",
                "full_text_search"
            ],
            "api_endpoints": {
                "main_api": "https://api.github.com"
            },
            "rate_limits": {
                "per_hour": self.config.get('rate_limit_per_hour', 5000)
            },
            "authentication": {
                "type": "personal_access_token",
                "required_fields": ["token"]
            },
            "connected": self.is_connected()
        }

    def test_connection(self) -> Dict[str, Any]:
        """
        Test the GitHub API connection.
        
        Returns:
            dict: Connection test results
        """
        try:
            if not self.connection:
                self.connect()
            
            return self.connection.get_connection_info()
            
        except Exception as e:
            logger.error(f"GitHub connection test failed: {str(e)}")
            return {
                "status": "failed",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }

    def fetch_data(self, **kwargs) -> Generator[Dict[str, Any], None, None]:
        """
        Fetch data from GitHub and yield individual entities.

        Yields:
            dict: Individual GitHub entities (repositories, issues, etc.)
        """
        try:
            logger.info("Starting comprehensive GitHub data fetch...")

            organisation_id = self.config.get('organisation_id')
            if not organisation_id:
                raise ValueError("Organisation ID is required for data fetching")

            # Use the existing service to perform sync and yield data
            success, message, repositories_synced, total_items_synced = self._github_service.sync_github(
                organisation_id, self.config.get('full_sync', False)
            )

            if not success:
                raise Exception(f"Sync failed: {message}")

            # For now, we'll yield a summary of the sync operation
            # In a full implementation, this would yield individual entities
            yield {
                'type': 'sync_summary',
                'repositories_synced': repositories_synced,
                'total_items_synced': total_items_synced,
                'message': message,
                'timestamp': datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"Error during GitHub data fetch: {str(e)}")
            raise

    def fetch_entity_by_id(self, id: str) -> Dict[str, Any]:
        """
        Fetch a specific GitHub entity by its ID.

        Args:
            id: Entity identifier to fetch

        Returns:
            dict: Entity data
        """
        try:
            logger.info(f"Fetching GitHub entity with ID: {id}")

            organisation_id = self.config.get('organisation_id')
            if not organisation_id:
                raise ValueError("Organisation ID is required for data fetching")

            # Use the existing service to sync a specific repository
            success, message, items_synced = self._github_service.sync_repository_by_url(
                github_url=f"https://github.com/{id}",  # Assuming id is in owner/repo format
                agent_id=self.config.get('agent_id', 'default'),
                user_id=self.config.get('user_id'),
                organisation_id=organisation_id,
                full_sync=False
            )

            if not success:
                raise Exception(f"Failed to fetch entity: {message}")

            return {
                'id': id,
                'success': success,
                'message': message,
                'items_synced': items_synced
            }

        except Exception as e:
            logger.error(f"Error fetching GitHub entity by ID {id}: {str(e)}")
            raise

    def search(self, query: str) -> ConnectorSearchResponse:
        """
        Search for data within the GitHub source.

        Args:
            query: Search query string

        Returns:
            ConnectorSearchResponse: Standardized search response

        Raises:
            ConnectionError: If not connected to data source
            Exception: For search errors
        """
        start_time = datetime.now()

        try:
            logger.info(f"Executing GitHub search: {query}")

            organisation_id = self.config.get('organisation_id')
            if not organisation_id:
                raise ValueError("Organisation ID is required for search")

            limit = self.config.get('search_limit', 10)

            # Use the GitHub service to search content
            results = self._github_service.search_github_content(
                organisation_id=organisation_id,
                query=query,
                limit=limit
            )

            # Convert results to SearchResultItem format
            search_items = []
            for result in results:
                item = SearchResultItem(
                    id=str(result.get('id', '')),
                    title=result.get('title', ''),
                    content=result.get('content', ''),
                    url=result.get('url', ''),
                    source=self.source_type,
                    entity_type=result.get('type', ''),
                    score=result.get('score', 0.0),
                    metadata=result.get('metadata', {})
                )
                search_items.append(item)

            # Calculate search time
            end_time = datetime.now()
            search_time_ms = int((end_time - start_time).total_seconds() * 1000)

            return ConnectorSearchResponse(
                status=SearchStatus.SUCCESS,
                results=search_items,
                total_count=len(search_items),
                metrics=SearchMetrics(
                    total_results=len(search_items),
                    search_time_ms=search_time_ms,
                    sources_searched=[self.source_type]
                ),
                query=query,
                connector_type=self.CONNECTOR_TYPE
            )

        except Exception as e:
            logger.error(f"Error during GitHub search: {str(e)}")
            return ConnectorSearchResponse(
                status=SearchStatus.ERROR,
                results=[],
                total_count=0,
                error=SearchError(
                    code="SEARCH_FAILED",
                    message=str(e)
                ),
                query=query,
                connector_type=self.CONNECTOR_TYPE
            )

    def sync(self):
        """
        Perform a full sync of the GitHub data source.

        This method:
        1. Fetches all data from GitHub
        2. Stores context and embeddings
        3. Updates knowledge graph

        Raises:
            ConnectionError: If not connected to data source
            Exception: For sync errors
        """
        try:
            logger.info("Starting full GitHub sync...")

            organisation_id = self.config.get('organisation_id')
            if not organisation_id:
                raise ValueError("Organisation ID is required for sync")

            # Use the existing service to perform sync
            success, message, repositories_synced, total_items_synced = self._github_service.sync_github(
                organisation_id, full_sync=True
            )

            if not success:
                raise Exception(f"Sync failed: {message}")

            logger.info(f"Completed full GitHub sync: {repositories_synced} repositories, {total_items_synced} total items")

        except Exception as e:
            logger.error(f"Error during GitHub full sync: {str(e)}")
            raise

    def sync_by_id(self, id: str):
        """
        Perform a partial sync for a single GitHub entity.

        Args:
            id: Entity identifier to sync (repository URL or owner/repo format)

        Raises:
            ValueError: If ID format is invalid
            ConnectionError: If not connected to data source
        """
        try:
            logger.info(f"Starting partial GitHub sync for ID: {id}")

            organisation_id = self.config.get('organisation_id')
            if not organisation_id:
                raise ValueError("Organisation ID is required for sync")

            # Convert ID to URL format if needed
            github_url = id if id.startswith('https://') else f"https://github.com/{id}"

            # Use the existing service to sync a specific repository
            success, message, items_synced = self._github_service.sync_repository_by_url(
                github_url=github_url,
                agent_id=self.config.get('agent_id', 'default'),
                user_id=self.config.get('user_id'),
                organisation_id=organisation_id,
                full_sync=False
            )

            if not success:
                raise Exception(f"Partial sync failed: {message}")

            logger.info(f"Completed partial GitHub sync for ID: {id}")

        except Exception as e:
            logger.error(f"Error during GitHub partial sync for ID {id}: {str(e)}")
            raise

    def store_context(self, data: Any):
        """
        Stores both context and embedding for given GitHub data.

        This method:
        1. Transforms data to standardized format
        2. Extracts entities and relationships
        3. Stores in knowledge graph (Neo4j)
        4. Generates and stores embeddings (Pinecone)

        Args:
            data: GitHub entity data to store (format varies by entity type)

        Raises:
            Exception: For storage errors
        """
        try:
            # The existing GitHub service handles context storage
            # through its internal methods during sync operations
            logger.debug(f"Stored context for GitHub entity")

        except Exception as e:
            logger.error(f"Error storing GitHub context: {str(e)}")
            raise

    def sync_by_url(self, url: str, agent_id: str, user_id: Optional[str] = None, full_sync: bool = False) -> Dict[str, Any]:
        """
        Sync a specific GitHub repository by URL.
        
        Args:
            url: GitHub repository URL
            agent_id: Agent ID performing the sync
            user_id: User ID (optional)
            full_sync: Whether to perform full sync
            
        Returns:
            dict: Sync results
        """
        try:
            logger.info(f"Starting GitHub repository sync by URL: {url}")
            
            organisation_id = self.config.get('organisation_id') if isinstance(self.config, dict) else getattr(self.config, 'organisation_id', None)
            if not organisation_id:
                raise ValueError("Organisation ID is required for sync")
            
            # Use the GitHub service to sync by URL
            success, message, items_synced = self._github_service.sync_repository_by_url(
                github_url=url,
                organisation_id=organisation_id,
                full_sync=full_sync
            )
            
            return {
                'success': success,
                'message': message,
                'items_synced': items_synced,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error during GitHub URL sync: {str(e)}")
            return {
                'success': False,
                'message': f"Sync failed: {str(e)}",
                'items_synced': 0,
                'timestamp': datetime.now().isoformat()
            }

    def get_sync_status(self) -> Dict[str, Any]:
        """
        Get the current sync status and statistics.
        
        Returns:
            dict: Sync status information
        """
        try:
            organisation_id = self.config.get('organisation_id') if isinstance(self.config, dict) else getattr(self.config, 'organisation_id', None)
            if not organisation_id:
                return {"error": "Organisation ID is required"}
            
            # Get sync statistics from the GitHub service
            stats = self._github_service.get_sync_statistics(organisation_id)
            
            return {
                'organisation_id': organisation_id,
                'connector_type': self.CONNECTOR_TYPE,
                'statistics': stats,
                'last_check': datetime.now().isoformat(),
                'connected': self._connected
            }
            
        except Exception as e:
            logger.error(f"Error getting GitHub sync status: {str(e)}")
            return {
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }

    def disconnect(self):
        """
        Disconnect from GitHub API and cleanup resources.
        """
        try:
            if self.connection:
                self.connection.disconnect()
                self.connection = None
            
            self._connected = False
            logger.info("Disconnected from GitHub API")
            
        except Exception as e:
            logger.error(f"Error during GitHub disconnect: {str(e)}")

    def health_check(self) -> Dict[str, Any]:
        """
        Perform a health check of the connector.

        Returns:
            dict: Health check results
        """
        try:
            if not self.is_connected():
                return {
                    'status': 'disconnected',
                    'message': 'Not connected to GitHub API',
                    'timestamp': datetime.now().isoformat()
                }

            # Test connection
            connection_info = self.test_connection()

            return {
                'status': 'healthy' if connection_info.get('status') == 'connected' else 'unhealthy',
                'connection_info': connection_info,
                'timestamp': datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"Error during GitHub health check: {str(e)}")
            return {
                'status': 'error',
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }

    def is_connected(self) -> bool:
        """
        Check if the connector is connected to GitHub API.

        Returns:
            bool: True if connected, False otherwise
        """
        return self._connected and self.connection is not None

    def validate_config(self) -> bool:
        """
        Validate connector configuration.

        Returns:
            bool: True if configuration is valid
        """
        try:
            required_fields = ['organisation_id']
            for field in required_fields:
                if not self.config.get(field):
                    logger.error(f"Missing required configuration field: {field}")
                    return False

            return True
        except Exception as e:
            logger.error(f"Error validating GitHub connector configuration: {str(e)}")
            return False
