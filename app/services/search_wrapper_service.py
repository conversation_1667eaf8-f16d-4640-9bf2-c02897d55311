import grpc
import structlog
from typing import Dict, Any, List

from app.grpc_ import search_pb2, search_pb2_grpc
from app.modules.connectors.handlers.gdrive.services.google_drive_service import GoogleDriveService
from app.modules.organisation.services.organisation import OrganisationService

logger = structlog.get_logger()

class SearchWrapperService(search_pb2_grpc.SearchServiceServicer):
    """
    Wrapper service for search operations.
    This service handles all search-related gRPC calls and delegates to appropriate handlers.
    """
    
    def __init__(self):
        self.google_drive_service = GoogleDriveService()
        self.organisation_service = OrganisationService()
        logger.info("SearchWrapperService initialized")

    def searchSimilarDocuments(self, request, context):
        """Search for documents semantically similar to a query"""
        print(f"Received search request: {request}")
        try:
            logger.info("Searching similar documents", 
                       query=request.query_text,
                       organisation_id=request.organisation_id)
            
            # Perform semantic search using Google Drive service
            success, message, results_data = self.google_drive_service.search_similar_documents(
                user_id=request.user_id if request.user_id else "system",
                query_text=request.query_text,
                top_k = getattr(request, 'top_k', 10),
                agent_id = getattr(request, 'agent_id', None),
                organisation_id=request.organisation_id,
                file_ids=list(request.source_ids) if request.source_ids else None,
                least_score=getattr(request, 'threshold', 0.7)
            )
            
            if not success:
                return search_pb2.SearchSimilarDocumentsResponse(
                    success=False,
                    message=message
                )
            print(f"Search results data: {results_data}")
            print(results_data)
            results = {'documents': results_data.results, 'total_count': len(results_data.results)}
            print(results)
            print(f"Search results: {results}")
            # Convert results to protobuf format
            search_results = []
            for result in results.get('documents', []):
                # Create EntityInfo
                entity_info = search_pb2.EntityInfo(
                    entity_id=result.get('id', ''),
                    entity_type=result.get('type', 'document'),
                    source_id=result.get('source_id', ''),
                    title=result.get('title', ''),
                    content_preview=result.get('content_preview', ''),
                    url=result.get('url', ''),
                    metadata=result.get('metadata', {})
                )
                
                # Create RelationshipInfo if available
                relationships = []
                for rel in result.get('relationships', []):
                    relationship_info = search_pb2.RelationshipInfo(
                        relationship_type=rel.get('type', ''),
                        related_entity_id=rel.get('entity_id', ''),
                        strength=rel.get('strength', 0.0),
                        metadata=rel.get('metadata', {})
                    )
                    relationships.append(relationship_info)
                
                # Create SearchResultItem
                search_result = search_pb2.SearchResultItem(
                    score=result.get('score', 0.0),
                    entity=entity_info,
                    relationships=relationships,
                    highlight_snippets=result.get('highlights', []),
                    access_level=result.get('access_level', 'public')
                )
                search_results.append(search_result)
            print(f"Search results: {search_results}")
            print(f"Total results: {len(search_results)}")
            print(f"Search time: {results.get('search_time_ms', 0)} ms")
            return search_pb2.SearchSimilarDocumentsResponse(
                success=True,
                results=search_results,
                total_results=results.get('total_count', len(search_results)),
                search_time_ms=results.get('search_time_ms', 0)
            )
            
        except Exception as e:
            print(e)
            logger.error("Error searching similar documents", error=str(e))
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Internal error: {str(e)}")
            return search_pb2.SearchSimilarDocumentsResponse(
                success=False,
                message=f"Error searching documents: {str(e)}"
            )

    def batchSearchSimilarDocuments(self, request, context):
        """Batch search for documents semantically similar to multiple queries"""
        try:
            logger.info("Batch searching similar documents", 
                       query_count=len(request.queries),
                       organisation_id=request.organisation_id)
            
            batch_results = []
            
            for query_request in request.queries:
                # Perform individual search for each query using Google Drive service
                success, message, results_data = self.google_drive_service.search_similar_documents(
                    user_id=query_request.user_id if query_request.user_id else "system",
                    query_text=query_request.query,
                    top_k=query_request.limit if query_request.limit else 10,
                    agent_id=query_request.agent_id if query_request.agent_id else None,
                    organisation_id=request.organisation_id,
                    file_ids=list(query_request.source_ids) if query_request.source_ids else None,
                    least_score=query_request.threshold if query_request.threshold else 0.7
                )
                
                if not success:
                    # Skip failed queries or add empty result
                    results = {'documents': [], 'total_count': 0, 'search_time_ms': 0}
                else:
                    results = {'documents': results_data, 'total_count': len(results_data), 'search_time_ms': 0}
                
                # Convert results to protobuf format
                search_results = []
                for result in results.get('documents', []):
                    entity_info = search_pb2.EntityInfo(
                        entity_id=result.get('id', ''),
                        entity_type=result.get('type', 'document'),
                        source_id=result.get('source_id', ''),
                        title=result.get('title', ''),
                        content_preview=result.get('content_preview', ''),
                        url=result.get('url', ''),
                        metadata=result.get('metadata', {})
                    )
                    
                    relationships = []
                    for rel in result.get('relationships', []):
                        relationship_info = search_pb2.RelationshipInfo(
                            relationship_type=rel.get('type', ''),
                            related_entity_id=rel.get('entity_id', ''),
                            strength=rel.get('strength', 0.0),
                            metadata=rel.get('metadata', {})
                        )
                        relationships.append(relationship_info)
                    
                    search_result = search_pb2.SearchResultItem(
                        score=result.get('score', 0.0),
                        entity=entity_info,
                        relationships=relationships,
                        highlight_snippets=result.get('highlights', []),
                        access_level=result.get('access_level', 'public')
                    )
                    search_results.append(search_result)
                
                # Create batch result
                batch_result = search_pb2.BatchSearchResult(
                    query=query_request.query,
                    results=search_results,
                    total_results=results.get('total_count', len(search_results)),
                    search_time_ms=results.get('search_time_ms', 0)
                )
                batch_results.append(batch_result)
            
            return search_pb2.BatchSearchSimilarDocumentsResponse(
                success=True,
                batch_results=batch_results,
                total_queries=len(request.queries),
                total_search_time_ms=sum(result.search_time_ms for result in batch_results)
            )
            
        except Exception as e:
            logger.error("Error in batch search", error=str(e))
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Internal error: {str(e)}")
            return search_pb2.BatchSearchSimilarDocumentsResponse(
                success=False,
                message=f"Error in batch search: {str(e)}"
            )

    def advancedSearch(self, request, context):
        """Search with advanced filters and options"""
        try:
            logger.info("Advanced search", 
                       query=request.query,
                       organisation_id=request.organisation_id)
            
            # Build advanced search parameters
            search_params = {
                'query': request.query,
                'organisation_id': request.organisation_id,
                'agent_id': request.agent_id if request.agent_id else None,
                'user_id': request.user_id if request.user_id else None,
                'limit': request.limit if request.limit else 10,
                'offset': request.offset if request.offset else 0
            }
            
            # Add filters
            if request.filters:
                filters = {}
                if request.filters.source_ids:
                    filters['source_ids'] = list(request.filters.source_ids)
                if request.filters.file_types:
                    filters['file_types'] = list(request.filters.file_types)
                if request.filters.date_range:
                    filters['date_range'] = {
                        'start': request.filters.date_range.start_date,
                        'end': request.filters.date_range.end_date
                    }
                if request.filters.tags:
                    filters['tags'] = list(request.filters.tags)
                if request.filters.departments:
                    filters['departments'] = list(request.filters.departments)
                
                search_params['filters'] = filters
            
            # Add sorting
            if request.sort_by:
                search_params['sort_by'] = request.sort_by
                search_params['sort_order'] = request.sort_order
            
            # For now, use basic search as advanced search placeholder
            success, message, results_data = self.google_drive_service.search_similar_documents(
                user_id=search_params.get('user_id', 'system'),
                query_text=search_params['query'],
                top_k=search_params.get('limit', 10),
                agent_id=search_params.get('agent_id'),
                organisation_id=search_params['organisation_id'],
                least_score=0.7
            )
            
            if not success:
                return search_pb2.AdvancedSearchResponse(
                    success=False,
                    message=message
                )
            
            results = {'documents': results_data, 'total_count': len(results_data), 'facets': [], 'search_time_ms': 0}
            
            # Convert results to protobuf format
            search_results = []
            for result in results.get('documents', []):
                entity_info = search_pb2.EntityInfo(
                    entity_id=result.get('id', ''),
                    entity_type=result.get('type', 'document'),
                    source_id=result.get('source_id', ''),
                    title=result.get('title', ''),
                    content_preview=result.get('content_preview', ''),
                    url=result.get('url', ''),
                    metadata=result.get('metadata', {})
                )
                
                relationships = []
                for rel in result.get('relationships', []):
                    relationship_info = search_pb2.RelationshipInfo(
                        relationship_type=rel.get('type', ''),
                        related_entity_id=rel.get('entity_id', ''),
                        strength=rel.get('strength', 0.0),
                        metadata=rel.get('metadata', {})
                    )
                    relationships.append(relationship_info)
                
                search_result = search_pb2.SearchResultItem(
                    score=result.get('score', 0.0),
                    entity=entity_info,
                    relationships=relationships,
                    highlight_snippets=result.get('highlights', []),
                    access_level=result.get('access_level', 'public')
                )
                search_results.append(search_result)
            
            # Create facets if available
            facets = []
            for facet_data in results.get('facets', []):
                facet = search_pb2.SearchFacet(
                    name=facet_data.get('name', ''),
                    values=facet_data.get('values', [])
                )
                facets.append(facet)
            
            return search_pb2.AdvancedSearchResponse(
                success=True,
                results=search_results,
                total_results=results.get('total_count', len(search_results)),
                facets=facets,
                search_time_ms=results.get('search_time_ms', 0),
                search_metadata=results.get('search_metadata', {})
            )
            
        except Exception as e:
            logger.error("Error in advanced search", error=str(e))
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Internal error: {str(e)}")
            return search_pb2.AdvancedSearchResponse(
                success=False,
                message=f"Error in advanced search: {str(e)}"
            )

    def getSearchSuggestions(self, request, context):
        """Get search suggestions/autocomplete"""
        try:
            logger.info("Getting search suggestions", 
                       query=request.query,
                       organisation_id=request.organisation_id)
            
            # Placeholder implementation for search suggestions
            suggestions = {
                'suggestions': [
                    f"{request.query} documents",
                    f"{request.query} files",
                    f"{request.query} reports"
                ][:request.limit if request.limit else 3],
                'search_time_ms': 0
            }
            
            return search_pb2.SearchSuggestionsResponse(
                success=True,
                suggestions=suggestions.get('suggestions', []),
                search_time_ms=suggestions.get('search_time_ms', 0)
            )
            
        except Exception as e:
            logger.error("Error getting search suggestions", error=str(e))
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Internal error: {str(e)}")
            return search_pb2.SearchSuggestionsResponse(
                success=False,
                message=f"Error getting suggestions: {str(e)}"
            )

    def searchBySource(self, request, context):
        """Search within a specific source"""
        try:
            logger.info("Searching by source", 
                       source_id=request.source_id,
                       query=request.query)
            
            # Use Google Drive service for source-specific search
            success, message, results_data = self.google_drive_service.search_similar_documents(
                user_id="system",
                query_text=request.query,
                top_k=request.limit if request.limit else 10,
                agent_id=None,
                organisation_id=request.organisation_id,
                file_ids=[request.source_id] if request.source_id else None,
                least_score=request.threshold if request.threshold else 0.7
            )
            
            if not success:
                return search_pb2.SearchBySourceResponse(
                    success=False,
                    message=message
                )
            
            results = {'documents': results_data, 'total_count': len(results_data), 'source_name': 'Google Drive', 'source_type': 'google_drive'}
            
            # Convert results to protobuf format
            search_results = []
            for result in results.get('documents', []):
                entity_info = search_pb2.EntityInfo(
                    entity_id=result.get('id', ''),
                    entity_type=result.get('type', 'document'),
                    source_id=result.get('source_id', ''),
                    title=result.get('title', ''),
                    content_preview=result.get('content_preview', ''),
                    url=result.get('url', ''),
                    metadata=result.get('metadata', {})
                )
                
                search_result = search_pb2.SearchResultItem(
                    score=result.get('score', 0.0),
                    entity=entity_info,
                    highlight_snippets=result.get('highlights', []),
                    access_level=result.get('access_level', 'public')
                )
                search_results.append(search_result)
            
            return search_pb2.SearchBySourceResponse(
                success=True,
                results=search_results,
                total_results=results.get('total_count', len(search_results)),
                source_info=search_pb2.SourceInfo(
                    source_id=request.source_id,
                    name=results.get('source_name', ''),
                    type=results.get('source_type', '')
                )
            )
            
        except Exception as e:
            logger.error("Error searching by source", error=str(e))
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Internal error: {str(e)}")
            return search_pb2.SearchBySourceResponse(
                success=False,
                message=f"Error searching by source: {str(e)}"
            )

    def getSearchAnalytics(self, request, context):
        """Get search analytics"""
        try:
            logger.info("Getting search analytics", 
                       organisation_id=request.organisation_id)
            
            # Placeholder implementation for search analytics
            analytics = {
                'total_searches': 0,
                'unique_queries': 0,
                'avg_response_time': 0.0,
                'top_queries': [],
                'search_trends': {},
                'metadata': {}
            }
            
            return search_pb2.SearchAnalyticsResponse(
                success=True,
                total_searches=analytics.get('total_searches', 0),
                unique_queries=analytics.get('unique_queries', 0),
                avg_response_time=analytics.get('avg_response_time', 0.0),
                top_queries=analytics.get('top_queries', []),
                search_trends=analytics.get('search_trends', {}),
                analytics_metadata=analytics.get('metadata', {})
            )
            
        except Exception as e:
            logger.error("Error getting search analytics", error=str(e))
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Internal error: {str(e)}")
            return search_pb2.SearchAnalyticsResponse(
                success=False,
                message=f"Error getting analytics: {str(e)}"
            )